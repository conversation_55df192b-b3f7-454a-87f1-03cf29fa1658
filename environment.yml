name: base
channels:
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - defaults
dependencies:
  - anaconda-anon-usage=0.7.1=py313hfc23b7f_100
  - anaconda_powershell_prompt=1.1.0=haa95532_1
  - anaconda_prompt=1.1.0=haa95532_1
  - annotated-types=0.6.0=py313haa95532_0
  - archspec=0.2.3=pyhd3eb1b0_0
  - boltons=25.0.0=py313haa95532_0
  - brotlicffi=*******=py313h5da7b33_1
  - bzip2=1.0.8=h2bbff1b_6
  - ca-certificates=2025.2.25=haa95532_0
  - certifi=2025.7.14=py313haa95532_0
  - cffi=1.17.1=py313h827c3e9_1
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - colorama=0.4.6=py313haa95532_0
  - conda=25.5.1=py313haa95532_0
  - conda-anaconda-telemetry=0.2.0=py313haa95532_1
  - conda-anaconda-tos=0.2.1=py313haa95532_0
  - conda-content-trust=0.2.0=py313haa95532_1
  - conda-libmamba-solver=25.4.0=pyhd3eb1b0_0
  - conda-package-handling=2.4.0=py313haa95532_0
  - conda-package-streaming=0.12.0=py313haa95532_0
  - cpp-expected=1.1.0=h214f63a_0
  - cryptography=45.0.3=py313h51e0144_0
  - distro=1.9.0=py313haa95532_0
  - expat=2.7.1=h8ddb27b_0
  - fmt=9.1.0=h6d14046_1
  - frozendict=2.4.2=py313haa95532_0
  - idna=3.7=py313haa95532_0
  - jsonpatch=1.33=py313haa95532_1
  - jsonpointer=2.1=pyhd3eb1b0_0
  - libarchive=3.7.7=h9243413_0
  - libcurl=8.14.1=ha9f67de_0
  - libffi=3.4.4=hd77b12b_1
  - libiconv=1.16=h2bbff1b_3
  - libmamba=2.0.5=hcd6fe79_1
  - libmambapy=2.0.5=py313h214f63a_1
  - libmpdec=4.0.0=h827c3e9_0
  - libsolv=0.7.30=hf2fb9eb_1
  - libssh2=1.11.1=h2addb87_0
  - libxml2=2.13.8=h866ff63_0
  - lz4-c=1.9.4=h2bbff1b_1
  - markdown-it-py=2.2.0=py313haa95532_1
  - mdurl=0.1.0=py313haa95532_0
  - menuinst=2.3.0=py313h5da7b33_0
  - nlohmann_json=3.11.2=h6c2663c_0
  - openssl=3.0.16=h3f729d1_0
  - packaging=24.2=py313haa95532_0
  - pcre2=10.42=h0ff8eda_1
  - pip=25.1=pyhc872135_2
  - platformdirs=4.3.7=py313haa95532_0
  - pluggy=1.5.0=py313haa95532_0
  - pybind11-abi=5=hd3eb1b0_0
  - pycosat=0.6.6=py313h827c3e9_2
  - pycparser=2.21=pyhd3eb1b0_0
  - pydantic=2.11.7=py313haa95532_0
  - pydantic-core=2.33.2=py313h215eeae_0
  - pygments=2.19.1=py313haa95532_0
  - pysocks=1.7.1=py313haa95532_0
  - python=3.13.5=h286a616_100_cp313
  - python_abi=3.13=0_cp313
  - reproc=14.2.4=hd77b12b_2
  - reproc-cpp=14.2.4=hd77b12b_2
  - requests=2.32.4=py313haa95532_0
  - rich=13.9.4=py313haa95532_0
  - ruamel.yaml=0.18.10=py313h827c3e9_0
  - ruamel.yaml.clib=0.2.12=py313h827c3e9_0
  - setuptools=78.1.1=py313haa95532_0
  - simdjson=3.10.1=h214f63a_0
  - spdlog=1.11.0=h59b6b97_0
  - sqlite=3.50.2=hda9a48d_1
  - tk=8.6.14=h5e9d12e_1
  - tqdm=4.67.1=py313h4442805_0
  - truststore=0.10.0=py313haa95532_0
  - typing-extensions=4.12.2=py313haa95532_0
  - typing-inspection=0.4.0=py313haa95532_0
  - typing_extensions=4.12.2=py313haa95532_0
  - tzdata=2025b=h04d1e81_0
  - ucrt=10.0.22621.0=haa95532_0
  - urllib3=2.5.0=py313haa95532_0
  - vc=14.3=h2df5915_10
  - vc14_runtime=14.44.35208=h4927774_10
  - vs2015_runtime=14.44.35208=ha6b5a95_10
  - wheel=0.45.1=py313haa95532_0
  - win_inet_pton=1.1.0=py313haa95532_0
  - xz=5.6.4=h4754444_1
  - yaml-cpp=0.8.0=hd77b12b_1
  - zlib=1.2.13=h8cc25b3_1
  - zstandard=0.23.0=py313h4fc1ca9_1
  - zstd=1.5.6=h8880b57_0
prefix: D:\applications\miniconda
