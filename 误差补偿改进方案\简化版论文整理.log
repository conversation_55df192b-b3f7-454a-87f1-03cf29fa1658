This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.4) (preloaded format=pdflatex 2025.7.26)  26 JUL 2025 12:59
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/Desktop/zytable/误差补偿改进方案/简化版论文整理.tex
(d:/Desktop/zytable/误差补偿改进方案/简化版论文整理.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-19>
(D:\applications\miktex\tex/latex/base\article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(D:\applications\miktex\tex/latex/base\size12.clo
File: size12.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count275
\c@section=\count276
\c@subsection=\count277
\c@subsubsection=\count278
\c@paragraph=\count279
\c@subparagraph=\count280
\c@figure=\count281
\c@table=\count282
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (D:\applications\miktex\tex/latex/ctex\ctex.sty (D:\applications\miktex\tex/latex/l3kernel\expl3.sty
Package: expl3 2025-07-19 L3 programming layer (loader) 
 (D:\applications\miktex\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2025-06-09 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count283
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)
 (D:\applications\miktex\tex/latex/ctex\ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (D:\applications\miktex\tex/latex/ctex\ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (D:\applications\miktex\tex/latex/base\fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (D:\applications\miktex\tex/latex/base\ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1) on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) on input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) on input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding TS1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) on input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) on input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) on input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input line 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on input line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encoding TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (encoding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encoding TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (encoding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1) on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encoding TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) on input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on input line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on input line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1) on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encoding TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) on input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1) on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1) on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1) on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on input line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on input line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on input line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on input line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) on input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on input line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) on input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) on input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) on input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on input line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on input line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on input line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1) on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on input line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) on input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on input line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1) on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on input line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding TS1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on input line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on input line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on input line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on input line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on input line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on input line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1) on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on input line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) on input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding TS1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on input line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on input line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) on input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1) on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on input line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on input line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on input line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on input line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) on input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) on input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1) on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on input line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1) on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1) on input line 177.
LaTeX Encoding Info:    Redeclaring text symbol \textpm (encoding TS1) on input line 178.
LaTeX Encoding Info:    Redeclaring text symbol \texttwosuperior (encoding TS1) on input line 179.
LaTeX Encoding Info:    Redeclaring text symbol \textthreesuperior (encoding TS1) on input line 180.
LaTeX Encoding Info:    Redeclaring text symbol \textasciiacute (encoding TS1) on input line 181.
LaTeX Encoding Info:    Redeclaring text symbol \textmu (encoding TS1) on input line 182.
LaTeX Encoding Info:    Redeclaring text symbol \textparagraph (encoding TS1) on input line 183.
LaTeX Encoding Info:    Redeclaring text symbol \textperiodcentered (encoding TS1) on input line 184.
LaTeX Encoding Info:    Redeclaring text symbol \textreferencemark (encoding TS1) on input line 185.
LaTeX Encoding Info:    Redeclaring text symbol \textonesuperior (encoding TS1) on input line 186.
LaTeX Encoding Info:    Redeclaring text symbol \textordmasculine (encoding TS1) on input line 187.
LaTeX Encoding Info:    Redeclaring text symbol \textsurd (encoding TS1) on input line 188.
LaTeX Encoding Info:    Redeclaring text symbol \textonequarter (encoding TS1) on input line 189.
LaTeX Encoding Info:    Redeclaring text symbol \textonehalf (encoding TS1) on input line 190.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequarters (encoding TS1) on input line 191.
LaTeX Encoding Info:    Redeclaring text symbol \texteuro (encoding TS1) on input line 192.
LaTeX Encoding Info:    Redeclaring text symbol \texttimes (encoding TS1) on input line 193.
LaTeX Encoding Info:    Redeclaring text symbol \textdiv (encoding TS1) on input line 194.
))
\l__ctex_tmp_int=\count284
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen149
\g__ctex_section_depth_int=\count285
\g__ctex_font_size_int=\count286
 (D:\applications\miktex\tex/latex/ctex/config\ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (D:\applications\miktex\tex/latex/ctex/engine\ctex-engine-pdftex.def
File: ctex-engine-pdftex.def 2022/07/14 v2.5.10 (pdf)LaTeX adapter (CTEX)
 (D:\applications\miktex\tex/latex/cjk\CJKutf8.sty
Package: CJKutf8 2021/10/16 4.8.5
 (D:\applications\miktex\tex/generic/iftex\ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (D:\applications\miktex\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)) (D:\applications\miktex\tex/latex/base\inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
) (D:\applications\miktex\tex/latex/cjk\CJK.sty
Package: CJK 2021/10/16 4.8.5
 (D:\applications\miktex\tex/latex/cjk/mule\MULEenc.sty
Package: MULEenc 2021/10/16 4.8.5
) (D:\applications\miktex\tex/latex/cjk\CJK.enc
File: CJK.enc 2021/10/16 4.8.5
Now handling font encoding C00 ...
... no UTF-8 mapping file for font encoding C00
Now handling font encoding C05 ...
... no UTF-8 mapping file for font encoding C05
Now handling font encoding C09 ...
... no UTF-8 mapping file for font encoding C09
Now handling font encoding C10 ...
... no UTF-8 mapping file for font encoding C10
Now handling font encoding C20 ...
... no UTF-8 mapping file for font encoding C20
Now handling font encoding C19 ...
... no UTF-8 mapping file for font encoding C19
Now handling font encoding C40 ...
... no UTF-8 mapping file for font encoding C40
Now handling font encoding C42 ...
... no UTF-8 mapping file for font encoding C42
Now handling font encoding C43 ...
... no UTF-8 mapping file for font encoding C43
Now handling font encoding C50 ...
... no UTF-8 mapping file for font encoding C50
Now handling font encoding C52 ...
... no UTF-8 mapping file for font encoding C52
Now handling font encoding C49 ...
... no UTF-8 mapping file for font encoding C49
Now handling font encoding C60 ...
... no UTF-8 mapping file for font encoding C60
Now handling font encoding C61 ...
... no UTF-8 mapping file for font encoding C61
Now handling font encoding C63 ...
... no UTF-8 mapping file for font encoding C63
Now handling font encoding C64 ...
... no UTF-8 mapping file for font encoding C64
Now handling font encoding C65 ...
... no UTF-8 mapping file for font encoding C65
Now handling font encoding C70 ...
... no UTF-8 mapping file for font encoding C70
Now handling font encoding C31 ...
... no UTF-8 mapping file for font encoding C31
Now handling font encoding C32 ...
... no UTF-8 mapping file for font encoding C32
Now handling font encoding C33 ...
... no UTF-8 mapping file for font encoding C33
Now handling font encoding C34 ...
... no UTF-8 mapping file for font encoding C34
Now handling font encoding C35 ...
... no UTF-8 mapping file for font encoding C35
Now handling font encoding C36 ...
... no UTF-8 mapping file for font encoding C36
Now handling font encoding C37 ...
... no UTF-8 mapping file for font encoding C37
Now handling font encoding C80 ...
... no UTF-8 mapping file for font encoding C80
Now handling font encoding C81 ...
... no UTF-8 mapping file for font encoding C81
Now handling font encoding C01 ...
... no UTF-8 mapping file for font encoding C01
Now handling font encoding C11 ...
... no UTF-8 mapping file for font encoding C11
Now handling font encoding C21 ...
... no UTF-8 mapping file for font encoding C21
Now handling font encoding C41 ...
... no UTF-8 mapping file for font encoding C41
Now handling font encoding C62 ...
... no UTF-8 mapping file for font encoding C62
)
\CJK@indent=\box54
) (D:\applications\miktex\tex/latex/base\fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
)) (D:\applications\miktex\tex/latex/cjkpunct\CJKpunct.sty
Package: CJKpunct 2016/05/14 4.8.4
\CJKpunct@cnta=\count287
\CJKpunct@cntb=\count288
\CJKpunct@cntc=\count289
\CJKpunct@cntd=\count290
\CJKpunct@cnte=\count291
   defining Unicode char U+2018 (decimal 8216)
   defining Unicode char U+2019 (decimal 8217)
   defining Unicode char U+201C (decimal 8220)
   defining Unicode char U+201D (decimal 8221)
   defining Unicode char U+2014 (decimal 8212)
   defining Unicode char U+2026 (decimal 8230)
 (D:\applications\miktex\tex/latex/cjkpunct\CJKpunct.spa)) (D:\applications\miktex\tex/latex/cjk\CJKspace.sty
Package: CJKspace 2021/10/16 3.8.0
) (D:\applications\miktex\tex/latex/cjk/UTF8\UTF8.bdg
File: UTF8.bdg 2021/10/16 4.8.5
) (D:\applications\miktex\tex/latex/ctex\ctexspa.def
File: ctexspa.def 2022/07/14 v2.5.10 Space info for CJKpunct (CTEX)
)
\ccwd=\dimen150
\l__ctex_ccglue_skip=\skip51
)
\l__ctex_ziju_dim=\dimen151
 (D:\applications\miktex\tex/latex/zhnumber\zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count292
\l__zhnum_tmp_int=\count293
 (D:\applications\miktex\tex/latex/zhnumber\zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
)) (D:\applications\miktex\tex/latex/ctex/scheme\ctex-scheme-chinese.def
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CTEX)
 (D:\applications\miktex\tex/latex/ctex/config\ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (D:\applications\miktex\tex/latex/tools\indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
) (D:\applications\miktex\tex/latex/ctex/fontset\ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)
)) (D:\applications\miktex\tex/latex/ctex/config\ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (D:\applications\miktex\tex/latex/amsmath\amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip52

For additional information on amsmath, use the `?' option.
(D:\applications\miktex\tex/latex/amsmath\amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text
 (D:\applications\miktex\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen152
)) (D:\applications\miktex\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen153
) (D:\applications\miktex\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count294
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count295
\leftroot@=\count296
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count297
\DOTSCASE@=\count298
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box55
\strutbox@=\box56
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen154
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count299
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count300
\dotsspace@=\muskip17
\c@parentequation=\count301
\dspbrk@lvl=\count302
\tag@help=\toks20
\row@=\count303
\column@=\count304
\maxfields@=\count305
\andhelp@=\toks21
\eqnshift@=\dimen155
\alignsep@=\dimen156
\tagshift@=\dimen157
\tagwidth@=\dimen158
\totwidth@=\dimen159
\lineht@=\dimen160
\@envbody=\toks22
\multlinegap=\skip53
\multlinetaggap=\skip54
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
) (D:\applications\miktex\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (D:\applications\miktex\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (D:\applications\miktex\tex/latex/algorithms\algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (D:\applications\miktex\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (D:\applications\miktex\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
)
\c@ALC@unique=\count306
\c@ALC@line=\count307
\c@ALC@rem=\count308
\c@ALC@depth=\count309
\ALC@tlm=\skip55
\algorithmicindent=\skip56
) (D:\applications\miktex\tex/latex/algorithms\algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (D:\applications\miktex\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count310
\float@exts=\toks25
\float@box=\box57
\@float@everytoks=\toks26
\@floatcapt=\box58
)
\@float@every@algorithm=\toks27
\c@algorithm=\count311
) (D:\applications\miktex\tex/latex/graphics\graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
 (D:\applications\miktex\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (D:\applications\miktex\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (D:\applications\miktex\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (D:\applications\miktex\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen161
\Gin@req@width=\dimen162
) (D:\applications\miktex\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (D:\applications\miktex\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count312
\Gm@cntv=\count313
\c@Gm@tempcnt=\count314
\Gm@bindingoffset=\dimen163
\Gm@wd@mp=\dimen164
\Gm@odd@mp=\dimen165
\Gm@even@mp=\dimen166
\Gm@layoutwidth=\dimen167
\Gm@layoutheight=\dimen168
\Gm@layouthoffset=\dimen169
\Gm@layoutvoffset=\dimen170
\Gm@dimlist=\toks28
 (D:\applications\miktex\tex/latex/geometry\geometry.cfg)) (D:\applications\miktex\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen171
\lightrulewidth=\dimen172
\cmidrulewidth=\dimen173
\belowrulesep=\dimen174
\belowbottomsep=\dimen175
\aboverulesep=\dimen176
\abovetopsep=\dimen177
\cmidrulesep=\dimen178
\cmidrulekern=\dimen179
\defaultaddspace=\dimen180
\@cmidla=\count315
\@cmidlb=\count316
\@aboverulesep=\dimen181
\@belowrulesep=\dimen182
\@thisruleclass=\count317
\@lastruleclass=\count318
\@thisrulewidth=\dimen183
) (D:\applications\miktex\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip57
\multirow@cntb=\count319
\multirow@dima=\skip58
\bigstrutjot=\dimen184
) (D:\applications\miktex\tex/latex/tools\bm.sty
Package: bm 2023/12/19 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (D:\applications\miktex\tex/latex/hyperref\hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX
 (D:\applications\miktex\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (D:\applications\miktex\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (D:\applications\miktex\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (D:\applications\miktex\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (D:\applications\miktex\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (D:\applications\miktex\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (D:\applications\miktex\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (D:\applications\miktex\tex/latex/hyperref\nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section
 (D:\applications\miktex\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (D:\applications\miktex\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (D:\applications\miktex\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count320
) (D:\applications\miktex\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count321
) (D:\applications\miktex\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen185
\Hy@linkcounter=\count322
\Hy@pagecounter=\count323
 (D:\applications\miktex\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (D:\applications\miktex\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count324
 (D:\applications\miktex\tex/latex/hyperref\puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `unicode' set `true' on input line 4066.
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count325
 (D:\applications\miktex\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen186
 (D:\applications\miktex\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (D:\applications\miktex\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count326
\Field@Width=\dimen187
\Fld@charsize=\dimen188
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring OFF on input line 6098.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\Hy@abspage=\count327
\c@Item=\count328
\c@Hfootnote=\count329
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (D:\applications\miktex\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2025-07-12 v7.01o Hyperref driver for pdfTeX
\Fld@listcount=\count330
\c@bookmark@seq@number=\count331
 (D:\applications\miktex\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)
 (D:\applications\miktex\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 284.
)
\Hy@SectionHShift=\skip59
) (D:\applications\miktex\tex/latex/cjk/UTF8\UTF8.enc
File: UTF8.enc 2021/10/16 4.8.5
) (D:\applications\miktex\tex/latex/cjk/UTF8\UTF8.chr
File: UTF8.chr 2021/10/16 4.8.5
) (简化版论文整理.aux)
\openout1 = `简化版论文整理.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C00/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C05/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C09/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C10/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C20/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C19/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C40/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C42/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C43/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C50/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C52/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C49/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C60/mj/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C61/mj/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C63/mj/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C64/mj/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C65/mj/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C70/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C31/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C32/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C33/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C34/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C35/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C36/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C37/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C80/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C81/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C01/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C11/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C21/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C41/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for C62/song/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 21.
LaTeX Font Info:    ... okay on input line 21.
 (D:\applications\miktex\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count332
\scratchdimen=\dimen189
\scratchbox=\box59
\nofMPsegments=\count333
\nofMParguments=\count334
\everyMPshowfont=\toks29
\MPscratchCnt=\count335
\MPscratchDim=\dimen190
\MPnumerator=\count336
\makeMPintoPDFobject=\count337
\everyMPtoPDFconversion=\toks30
) (D:\applications\miktex\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (D:\applications\miktex\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 21.
(简化版论文整理.out) (简化版论文整理.out)
\@outlinefile=\write3
\openout3 = `简化版论文整理.out'.

LaTeX Font Info:    Trying to load font information for C70+rm on input line 24.
 (D:\applications\miktex\tex/latex/ctex/fd\c70rm.fd
File: c70rm.fd 2022/07/14 v2.5.10 Chinese font definition (CTEX)
)
Package CJKpunct Info: use punctuation spaces for family 'rm' with punctstyle (quanjiao) on input line 24.
LaTeX Font Info:    Trying to load font information for U+msa on input line 24.
 (D:\applications\miktex\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 24.
 (D:\applications\miktex\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

(D:\applications\miktex\tex/generic/ctex/zhmap\ctex-zhmap-windows.tex
File: ctex-zhmap-windows.tex 2022/07/14 v2.5.10 Windows font map loader for pdfTeX and DVIPDFMx (CTEX)
{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{UGBK.sfd}{Unicode.sfd}) [1

]
<fig1_loss_landscape.png, id=252, 983.3538pt x 419.166pt>
File: fig1_loss_landscape.png Graphic file (type png)
<use fig1_loss_landscape.png>
Package pdftex.def Info: fig1_loss_landscape.png  used on input line 85.
(pdftex.def)             Requested size: 409.71692pt x 174.6478pt.


[2 <./fig1_loss_landscape.png>]
<fig4_attention_mechanism.png, id=281, 1289.2968pt x 348.8232pt>
File: fig4_attention_mechanism.png Graphic file (type png)
<use fig4_attention_mechanism.png>
Package pdftex.def Info: fig4_attention_mechanism.png  used on input line 116.
(pdftex.def)             Requested size: 432.48051pt x 117.00685pt.
<fig2_physics_constraints.png, id=284, 1144.275pt x 854.7132pt>
File: fig2_physics_constraints.png Graphic file (type png)
<use fig2_physics_constraints.png>
Package pdftex.def Info: fig2_physics_constraints.png  used on input line 145.
(pdftex.def)             Requested size: 432.48051pt x 323.03374pt.


[3 <./fig4_attention_mechanism.png>]

[4 <./fig2_physics_constraints.png>]
<fig3_multi_objective.png, id=314, 1288.815pt x 420.6114pt>
File: fig3_multi_objective.png Graphic file (type png)
<use fig3_multi_objective.png>
Package pdftex.def Info: fig3_multi_objective.png  used on input line 186.
(pdftex.def)             Requested size: 432.48051pt x 141.14476pt.


[5]
<fig5_deterministic_comparison.png, id=341, 1148.6112pt x 858.8085pt>
File: fig5_deterministic_comparison.png Graphic file (type png)
<use fig5_deterministic_comparison.png>
Package pdftex.def Info: fig5_deterministic_comparison.png  used on input line 214.
(pdftex.def)             Requested size: 432.48051pt x 323.36282pt.


[6 <./fig3_multi_objective.png>]

[7 <./fig5_deterministic_comparison.png>]

[8]

[9] (简化版论文整理.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2022/07/14>
 ***********
Package rerunfilecheck Info: File `简化版论文整理.out' has not changed.
(rerunfilecheck)             Checksum: 0A6677CFEA7B1743D0184107E5FF9ADA;2369.
 ) 
Here is how much of TeX's memory you used:
 15320 strings out of 468149
 249013 string characters out of 5441664
 655667 words of memory out of 5000000
 43264 multiletter control sequences out of 15000+600000
 701383 words of font info for 312 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 80i,11n,83p,787b,481s stack positions out of 10000i,1000n,20000p,200000b,200000s
<C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcrm1200.pk><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simsun.ttc><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><C:/WINDOWS/Fonts/simhei.ttf><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmbx12.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmex10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmi12.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmi6.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmi8.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmmib10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmr12.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmr8.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmsy10.pfb><D:/applications/miktex/fonts/type1/public/amsfonts/cm/cmsy8.pfb>
Output written on 简化版论文整理.pdf (9 pages, 4163581 bytes).
PDF statistics:
 818 PDF objects out of 1000 (max. 8388607)
 76 named destinations out of 1000 (max. 500000)
 482 words of extra memory for PDF output out of 10000 (max. 10000000)

